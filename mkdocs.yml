# Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Project information
site_name: PaddleScience Docs
site_description: An out-of-the-box open source library for scientific computing based on deep learning.
site_author: PaddlePaddle
site_url: "https://github.com/PaddlePaddle/PaddleScience"
edit_uri: edit/develop/docs/
strict: false

# Repository
repo_name: PaddleScience
repo_url: "https://github.com/PaddlePaddle/PaddleScience"

# Copyright
copyright: Copyright &copy; 2022 - 2023 PaddlePaddle

# Page tree
nav:
  - PaddleScience:
      - 主页: index.md
      - 功能介绍: zh/overview.md
      - 安装使用: zh/install_setup.md
      - 快速开始: zh/quickstart.md
      - 多硬件支持: zh/multi_device.md
      - 学习资料: zh/tutorials.md
  - 经典案例:
    - " ":
      - 数学(AI for Math):
        - AllenCahn: zh/examples/allen_cahn.md
        - DeepHPMs: zh/examples/deephpms.md
        - DeepONet: zh/examples/deeponet.md
        - Euler_Beam: zh/examples/euler_beam.md
        - Laplace2D: zh/examples/laplace2d.md
        - Lorenz_transform_physx: zh/examples/lorenz.md
        - PIRBN: zh/examples/pirbn.md
        - Rossler_transform_physx: zh/examples/rossler.md
        - Volterra_IDE: zh/examples/volterra_ide.md
        - NLSMB: zh/examples/nlsmb.md
        - SPINN: zh/examples/spinn.md
        - XPINN: zh/examples/xpinns.md
        - NeuralOperator: zh/examples/neuraloperator.md
        - Brusselator3D: zh/examples/brusselator3d.md
        - Transformer4SR: zh/examples/transformer4sr.md
      - 技术科学(AI for Technology):
        - 流体:
          - Fundiff: zh/examples/fundiff.md
          - Catheter: zh/examples/catheter.md
          - AMGNet: zh/examples/amgnet.md
          - Aneurysm: zh/examples/aneurysm.md
          - BubbleNet: zh/examples/bubble.md
          - CFDGCN: zh/examples/cfdgcn.md
          - CVit(Advection): zh/examples/adv_cvit.md
          - CVit(NS): zh/examples/ns_cvit.md
          - Cylinder2D_unsteady: zh/examples/cylinder2d_unsteady.md
          - Cylinder2D_unsteady_transform_physx: zh/examples/cylinder2d_unsteady_transformer_physx.md
          - Darcy2D: zh/examples/darcy2d.md
          - DeepCFD: zh/examples/deepcfd.md
          - DrivAerNet: zh/examples/drivaernet.md
          - DrivAerNetPlusPlus: zh/examples/drivaernetplusplus.md
          - LDC2D_steady: zh/examples/ldc2d_steady.md
          - LDC2D_unsteady: zh/examples/ldc2d_unsteady.md
          - Labelfree_DNN_surrogate: zh/examples/labelfree_DNN_surrogate.md
          - NSFNets: zh/examples/nsfnet.md
          - PhyCRNet: zh/examples/phycrnet.md
          - ShockWave: zh/examples/shock_wave.md
          - tempoGAN: zh/examples/tempoGAN.md
          - NSFNet4: zh/examples/nsfnet4.md
          - ViV: zh/examples/viv.md
        - 结构:
          - Biharmonic2D: zh/examples/biharmonic2d.md
          - Bracket: zh/examples/bracket.md
          - Control_arm: zh/examples/control_arm.md
          - EPNN: zh/examples/epnn.md
          - Phy-LSTM: zh/examples/phylstm.md
          - TopOpt: zh/examples/topopt.md
          - NTopo: zh/examples/ntopo.md
          - Heart: zh/examples/heart.md
        - 传热:
          - Heat_Exchanger: zh/examples/heat_exchanger.md
          - Heat_PINN: zh/examples/heat_pinn.md
          - PhyGeoNet: zh/examples/phygeonet.md
          - Chip_heat: zh/examples/chip_heat.md
      - 材料科学(AI for Material):
        - hPINNs: zh/examples/hpinns.md
        - CGCNN: zh/examples/cgcnn.md
        - psc_NN: zh/examples/perovskite_solar_cells_nn.md
        - Battery_LI: zh/examples/MLP_LI.md
        - ML2DDB: en/examples/ml2ddb.md
      - 地球科学(AI for Earth Science):
        - Extformer-MoE: zh/examples/extformer_moe.md
        - FourCastNet: zh/examples/fourcastnet.md
        - NowcastNet: zh/examples/nowcastnet.md
        - DGMR: zh/examples/dgmr.md
        - STAFNet: zh/examples/stafnet.md
        - EarthFormer: zh/examples/earthformer.md
        - GraphCast: zh/examples/graphcast.md
        - GenCast: zh/examples/gencast.md
        - VelocityGAN: zh/examples/velocity_gan.md
        - TGCN: zh/examples/tgcn.md
        - IOPS: zh/examples/iops.md
        - Pang-Weather: zh/examples/pangu_weather.md
        - FengWu: zh/examples/fengwu.md
        - FuXi: zh/examples/fuxi.md
        - UNetFormer: zh/examples/unetformer.md
        - WGAN_GP: zh/examples/wgan_gp.md
      - 化学科学(AI for Chemistry):
        - Moflow: zh/examples/moflow.md
        - IFM: zh/examples/ifm.md

  - API 文档:
    - ppsci:
      - ppsci.arch: zh/api/arch.md
      - ppsci.autodiff: zh/api/autodiff.md
      - ppsci.constraint: zh/api/constraint.md
      - ppsci.data:
        - ppsci.data.dataset: zh/api/data/dataset.md
        - ppsci.data.transform: zh/api/data/process/transform.md
        - ppsci.data.batch_transform: zh/api/data/process/batch_transform.md
      - ppsci.equation: zh/api/equation.md
      - ppsci.geometry: zh/api/geometry.md
      - ppsci.loss:
        - ppsci.loss.loss: zh/api/loss/loss.md
        - ppsci.loss.mtl: zh/api/loss/mtl.md
      - ppsci.metric: zh/api/metric.md
      - ppsci.optimizer:
        - ppsci.optimizer.optimizer: zh/api/optimizer.md
        - ppsci.optimizer.lr_scheduler: zh/api/lr_scheduler.md
      - ppsci.solver: zh/api/solver.md
      - ppsci.utils:
        - ppsci.utils.checker: zh/api/utils/checker.md
        - ppsci.utils.expression: zh/api/utils/expression.md
        - ppsci.utils.initializer: zh/api/utils/initializer.md
        - ppsci.utils.logger: zh/api/utils/logger.md
        - ppsci.utils.misc: zh/api/utils/misc.md
        - ppsci.utils.ema: zh/api/utils/ema.md
        - ppsci.utils.reader: zh/api/utils/reader.md
        - ppsci.utils.writer: zh/api/utils/writer.md
        - ppsci.utils.save_load: zh/api/utils/save_load.md
        - ppsci.utils.symbolic: zh/api/utils/symbolic.md
      - ppsci.validate: zh/api/validate.md
      - ppsci.visualize: zh/api/visualize.md
      - ppsci.experimental: zh/api/experimental.md
      - ppsci.probability: zh/api/probability.md
    - deploy:
      - deploy.python_infer: zh/api/depoly/python_infer.md
  - 使用指南: zh/user_guide.md
  - 开发与复现指南:
    - 开发指南: zh/development.md
    - 复现指南: zh/reproduction.md
  - 学术成果: zh/academic.md
  - 技术稿件: zh/technical_doc.md
  - 共创计划: zh/cooperation.md
  - 实训社区: https://aistudio.baidu.com/aistudio/projectoverview/public?topic=15

# Configuration
theme:
  name: material
  font:
    code: Roboto Mono
    text: Noto Serif SC
  logo: ./images/icons/paddle.png
  favicon: ./images/icons/favcion.png
  language: "zh"
  features:
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.tracking
    - navigation.sections
    # - navigation.prune # disabled temporarily for rendering speed
    - navigation.top
    - search.suggest
    - navigation.instant
    - content.code.copy
    - content.code.select
    - content.code.annotate
    - search.highlight
    - content.tabs.link
    - content.action.edit
    - content.action.view
    - content.tooltips
  icon:
    edit: material/pencil
    view: material/eye
    annotation: fontawesome/solid/circle-question
  palette:
    # Palette toggle for light mode
    - scheme: default
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode

    # Palette toggle for dark mode
    - scheme: slate
      toggle:
        icon: material/brightness-4
        name: Switch to light mode

# Plugins
plugins:
  - include-markdown
  - search
  - glightbox
  - mkdocstrings:
      handlers:
        python:
          setup_commands:
            - import sys
            - sys.path.append("../")
          paths: [../ppsci]
          selection:
            new_path_syntax: true
  - mkdocs-video
  - git-revision-date-localized:
      enable_creation_date: true
      fallback_to_build_date: true
      type: date
  - mike

# Extensions
markdown_extensions:
  - abbr
  - pymdownx.critic
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.snippets:
      dedent_subsections: true
  - pymdownx.arithmatex:
      generic: true
  - toc:
      permalink: true
  - pymdownx.inlinehilite
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - admonition
  - pymdownx.details
  - attr_list
  - md_in_html
  - pymdownx.tabbed:
      alternate_style: true

# Extra javascripts
extra_javascript:
  - javascripts/bd_statistics.js
  - javascripts/mathjax.js
  - javascripts/contributors.js
  - https://polyfill.io/v3/polyfill.min.js?features=es6
  - https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js

# Extra css
extra_css:
  - stylesheets/extra.css

# Extra
extra:
  alternate:
    - name: 简体中文
      link: /zh/
      lang: zh
    - name: English
      link: /en/
      lang: en
  version:
    provider: mike
    alias: true
